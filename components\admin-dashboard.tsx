"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  Activity,
  Brain,
  Code,
  Database,
  GitBranch,
  Play,
  Pause,
  Settings,
  Users,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
} from "lucide-react"

interface Agent {
  id: string
  name: string
  type: string
  status: "active" | "idle" | "error"
  currentTask?: string
  completedTasks: number
  icon: any
}

interface Project {
  id: string
  name: string
  status: "planning" | "development" | "testing" | "completed"
  progress: number
  tasksTotal: number
  tasksCompleted: number
  agents: string[]
}

interface AdminDashboardProps {
  onBack: () => void
}

export default function AdminDashboard({ onBack }: AdminDashboardProps) {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: "1",
      name: "Project Planning Agent",
      type: "planning",
      status: "active",
      currentTask: "Analyzing new e-commerce project requirements",
      completedTasks: 12,
      icon: Brain,
    },
    {
      id: "2",
      name: "Context Engine",
      type: "analysis",
      status: "active",
      currentTask: "Ingesting React codebase (2.3M LOC)",
      completedTasks: 45,
      icon: Database,
    },
    {
      id: "3",
      name: "Task Planning Agent",
      type: "coordination",
      status: "active",
      currentTask: "Breaking down authentication feature",
      completedTasks: 28,
      icon: Users,
    },
    {
      id: "4",
      name: "Frontend Coding Agent",
      type: "coding",
      status: "active",
      currentTask: "Implementing user dashboard components",
      completedTasks: 67,
      icon: Code,
    },
    {
      id: "5",
      name: "Backend Coding Agent",
      type: "coding",
      status: "idle",
      completedTasks: 34,
      icon: GitBranch,
    },
    {
      id: "6",
      name: "Testing Agent",
      type: "testing",
      status: "active",
      currentTask: "Running integration tests for API endpoints",
      completedTasks: 23,
      icon: CheckCircle,
    },
  ])

  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      name: "E-commerce Platform",
      status: "development",
      progress: 65,
      tasksTotal: 45,
      tasksCompleted: 29,
      agents: ["1", "2", "3", "4"],
    },
    {
      id: "2",
      name: "Analytics Dashboard",
      status: "testing",
      progress: 85,
      tasksTotal: 32,
      tasksCompleted: 27,
      agents: ["2", "4", "6"],
    },
    {
      id: "3",
      name: "Mobile App Backend",
      status: "planning",
      progress: 15,
      tasksTotal: 28,
      tasksCompleted: 4,
      agents: ["1", "3"],
    },
  ])

  const [systemMetrics, setSystemMetrics] = useState({
    totalProjects: 3,
    activeAgents: 5,
    tasksCompleted: 239,
    systemUptime: "99.8%",
    avgResponseTime: "120ms",
    codeQualityScore: 94,
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500"
      case "idle":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      case "planning":
        return "bg-blue-500"
      case "development":
        return "bg-red-500"
      case "testing":
        return "bg-orange-500"
      case "completed":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Play className="w-4 h-4" />
      case "idle":
        return <Pause className="w-4 h-4" />
      case "error":
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Editor
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                <span className="font-bold text-white">APB</span>
                <span className="font-bold text-red-500">X</span>
                <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                AI Development Ecosystem - Admin
              </h1>
              <p className="text-gray-600">System monitoring and agent management</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              System Online
            </Badge>
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold">{systemMetrics.totalProjects}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Agents</p>
                  <p className="text-2xl font-bold">{systemMetrics.activeAgents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Tasks Completed</p>
                  <p className="text-2xl font-bold">{systemMetrics.tasksCompleted}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">System Uptime</p>
                  <p className="text-2xl font-bold">{systemMetrics.systemUptime}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-indigo-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg Response</p>
                  <p className="text-2xl font-bold">{systemMetrics.avgResponseTime}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Code className="w-5 h-5 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Code Quality</p>
                  <p className="text-2xl font-bold">{systemMetrics.codeQualityScore}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="agents" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="agents">AI Agents</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="agents" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map((agent) => {
                const IconComponent = agent.icon
                return (
                  <Card key={agent.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-50 rounded-lg">
                            <IconComponent className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{agent.name}</CardTitle>
                            <CardDescription className="capitalize">{agent.type}</CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                          {getStatusIcon(agent.status)}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {agent.currentTask && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Current Task:</p>
                            <p className="text-sm text-gray-600">{agent.currentTask}</p>
                          </div>
                        )}
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Completed Tasks:</span>
                          <Badge variant="secondary">{agent.completedTasks}</Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                            View Logs
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                            Configure
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>

          <TabsContent value="projects" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {projects.map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{project.name}</CardTitle>
                        <CardDescription className="capitalize">{project.status} phase</CardDescription>
                      </div>
                      <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{project.progress}%</span>
                      </div>
                      <Progress value={project.progress} className="h-2" />
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span>Tasks:</span>
                      <span>
                        {project.tasksCompleted}/{project.tasksTotal} completed
                      </span>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">Active Agents:</p>
                      <div className="flex flex-wrap gap-2">
                        {project.agents.map((agentId) => {
                          const agent = agents.find((a) => a.id === agentId)
                          return agent ? (
                            <Badge key={agentId} variant="outline" className="text-xs">
                              {agent.name.split(" ")[0]}
                            </Badge>
                          ) : null
                        })}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button size="sm" className="flex-1">
                        View Details
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                        Manage Tasks
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Performance</CardTitle>
                  <CardDescription>Real-time system metrics and health indicators</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">CPU Usage</span>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                    <Progress value={45} className="h-2" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Memory Usage</span>
                      <span className="text-sm font-medium">62%</span>
                    </div>
                    <Progress value={62} className="h-2" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Network I/O</span>
                      <span className="text-sm font-medium">28%</span>
                    </div>
                    <Progress value={28} className="h-2" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Database Load</span>
                      <span className="text-sm font-medium">71%</span>
                    </div>
                    <Progress value={71} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Agent Productivity</CardTitle>
                  <CardDescription>Task completion rates and efficiency metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    {agents.slice(0, 4).map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                          <span className="text-sm font-medium">{agent.name.split(" ")[0]}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">{agent.completedTasks} tasks</span>
                          <Badge variant="outline" className="text-xs">
                            {Math.floor(Math.random() * 20 + 80)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
