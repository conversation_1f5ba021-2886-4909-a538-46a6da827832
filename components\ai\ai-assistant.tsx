"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Brain,
  Zap,
  Code,
  FileText,
  Database,
  TestTube,
  CheckCircle,
  Clock,
  AlertCircle,
  Play,
  Pause,
  Square,
} from "lucide-react"

interface Task {
  id: string
  title: string
  description: string
  status: "pending" | "running" | "completed" | "error"
  progress: number
  type: "code" | "file" | "database" | "test"
  estimatedTime?: string
}

interface AIAssistantProps {
  isActive?: boolean
  currentTask?: string
  tasks?: Task[]
  onStart?: () => void
  onPause?: () => void
  onStop?: () => void
}

export default function AIAssistant({
  isActive = false,
  currentTask = "Analyzing project structure...",
  tasks = [],
  onStart,
  onP<PERSON><PERSON>,
  onStop,
}: AIAssistantProps) {
  const [assistant<PERSON><PERSON>, setAssistantState] = useState<"idle" | "thinking" | "working">(isActive ? "working" : "idle")

  const getTaskIcon = (type: string) => {
    switch (type) {
      case "code":
        return <Code className="w-4 h-4" />
      case "file":
        return <FileText className="w-4 h-4" />
      case "database":
        return <Database className="w-4 h-4" />
      case "test":
        return <TestTube className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400"
      case "running":
        return "text-blue-400"
      case "error":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case "running":
        return <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const completedTasks = tasks.filter((task) => task.status === "completed").length
  const totalProgress = tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0

  return (
    <Card className="bg-[#1a1a1a] border-[#333]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              {isActive && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                </div>
              )}
            </div>
            <div>
              <CardTitle className="text-white text-lg">AI Assistant</CardTitle>
              <p className="text-gray-400 text-sm">
                {assistantState === "idle" && "Ready to help"}
                {assistantState === "thinking" && "Processing your request..."}
                {assistantState === "working" && currentTask}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isActive ? "default" : "secondary"} className="bg-blue-500">
              {isActive ? "Active" : "Idle"}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Overview */}
        {tasks.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Overall Progress</span>
              <span className="text-white">{Math.round(totalProgress)}%</span>
            </div>
            <Progress value={totalProgress} className="h-2" />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>
                {completedTasks} of {tasks.length} tasks completed
              </span>
              <div className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                <span>AI-powered</span>
              </div>
            </div>
          </div>
        )}

        {/* Task List */}
        {tasks.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white">Current Tasks</h4>
            <div className="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
              {tasks.slice(0, 5).map((task) => (
                <div
                  key={task.id}
                  className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                    task.status === "running" ? "bg-blue-500/10 border border-blue-500/20" : "bg-[#2a2a2a]"
                  }`}
                >
                  <div className="flex-shrink-0">{getStatusIcon(task.status)}</div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium truncate ${getStatusColor(task.status)}`}>{task.title}</p>
                    {task.description && <p className="text-xs text-gray-500 truncate">{task.description}</p>}
                    {task.status === "running" && task.progress > 0 && (
                      <div className="mt-1">
                        <Progress value={task.progress} className="h-1" />
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0 text-blue-400">{getTaskIcon(task.type)}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center gap-2 pt-2 border-t border-[#333]">
          {!isActive ? (
            <Button onClick={onStart} className="flex-1 bg-blue-500 hover:bg-blue-600">
              <Play className="w-4 h-4 mr-2" />
              Start Assistant
            </Button>
          ) : (
            <>
              <Button onClick={onPause} variant="outline" className="flex-1 bg-transparent">
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
              <Button onClick={onStop} variant="outline" className="flex-1 bg-transparent">
                <Square className="w-4 h-4 mr-2" />
                Stop
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
