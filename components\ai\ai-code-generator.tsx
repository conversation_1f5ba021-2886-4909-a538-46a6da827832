"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Code, Play, Square, Copy, Download, Sparkles, Zap, CheckCircle } from "lucide-react"

interface AICodeGeneratorProps {
  onGenerate?: (prompt: string, language: string) => void
  isGenerating?: boolean
  generatedCode?: string
}

export default function AICodeGenerator({
  onGenerate,
  isGenerating = false,
  generatedCode = "",
}: AICodeGeneratorProps) {
  const [prompt, setPrompt] = useState("")
  const [language, setLanguage] = useState("typescript")
  const [streamedCode, setStreamedCode] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)

  const sampleCode = `import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function TodoApp() {
  const [todos, setTodos] = useState([])
  const [input, setInput] = useState('')

  const addTodo = () => {
    if (input.trim()) {
      setTodos([...todos, { id: Date.now(), text: input, completed: false }])
      setInput('')
    }
  }

  return (
    <div className="max-w-md mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Todo App</h1>
      <div className="flex gap-2 mb-4">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Add a todo..."
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
        />
        <Button onClick={addTodo}>Add</Button>
      </div>
      <ul className="space-y-2">
        {todos.map((todo) => (
          <li key={todo.id} className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span className={todo.completed ? 'line-through' : ''}>
              {todo.text}
            </span>
          </li>
        ))}
      </ul>
    </div>
  )
}`

  useEffect(() => {
    if (isGenerating && currentIndex < sampleCode.length) {
      const timer = setTimeout(() => {
        setStreamedCode(sampleCode.slice(0, currentIndex + 1))
        setCurrentIndex(currentIndex + 1)
      }, 20)
      return () => clearTimeout(timer)
    }
  }, [isGenerating, currentIndex, sampleCode.length])

  useEffect(() => {
    if (!isGenerating) {
      setCurrentIndex(0)
      setStreamedCode("")
    }
  }, [isGenerating])

  const handleGenerate = () => {
    if (prompt.trim() && onGenerate) {
      onGenerate(prompt, language)
    }
  }

  const copyCode = () => {
    navigator.clipboard.writeText(streamedCode || generatedCode)
  }

  const downloadCode = () => {
    const element = document.createElement("a")
    const file = new Blob([streamedCode || generatedCode], { type: "text/plain" })
    element.href = URL.createObjectURL(file)
    element.download = `generated-code.${language === "typescript" ? "tsx" : language}`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  return (
    <div className="space-y-4">
      {/* Input Section */}
      <Card className="bg-[#1a1a1a] border-[#333]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-400" />
            AI Code Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-300 mb-2 block">Describe what you want to build</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="e.g., Create a React todo app with add, delete, and toggle functionality..."
              className="bg-[#2a2a2a] border-[#444] text-white min-h-[100px] focus:border-blue-500"
            />
          </div>

          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-300 mb-2 block">Language</label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="bg-[#2a2a2a] border-[#444] text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#2a2a2a] border-[#444]">
                  <SelectItem value="typescript">TypeScript</SelectItem>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="java">Java</SelectItem>
                  <SelectItem value="go">Go</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={handleGenerate}
                disabled={!prompt.trim() || isGenerating}
                className="bg-blue-500 hover:bg-blue-600"
              >
                {isGenerating ? (
                  <>
                    <Square className="w-4 h-4 mr-2" />
                    Stop
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Generate
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Section */}
      {(isGenerating || streamedCode || generatedCode) && (
        <Card className="bg-[#1a1a1a] border-[#333]">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Code className="w-5 h-5 text-green-400" />
                <CardTitle className="text-white">Generated Code</CardTitle>
                {isGenerating && (
                  <Badge className="bg-blue-500">
                    <Zap className="w-3 h-3 mr-1" />
                    Generating...
                  </Badge>
                )}
                {!isGenerating && streamedCode && (
                  <Badge className="bg-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Complete
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={copyCode}>
                  <Copy className="w-4 h-4 mr-1" />
                  Copy
                </Button>
                <Button size="sm" variant="outline" onClick={downloadCode}>
                  <Download className="w-4 h-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="bg-[#0d1117] rounded-lg p-4 font-mono text-sm overflow-auto max-h-96 custom-scrollbar">
              <pre className="text-gray-300 whitespace-pre-wrap">
                {streamedCode || generatedCode}
                {isGenerating && <span className="inline-block w-2 h-4 bg-blue-400 animate-pulse ml-1" />}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
