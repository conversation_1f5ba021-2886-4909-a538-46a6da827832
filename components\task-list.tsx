"use client"

import { useState, useEffect } from "react"
import { CheckCircle, Circle, Loader2, FileText, Code, Database, TestTube } from "lucide-react"

interface Task {
  id: string
  title: string
  status: "pending" | "running" | "completed" | "error"
  type: "file" | "code" | "database" | "test"
  progress?: number
}

interface TaskListProps {
  tasks: Task[]
  title?: string
}

export default function TaskList({ tasks, title = "Agent Tasks" }: TaskListProps) {
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      const runningTaskIndex = tasks.findIndex((task) => task.status === "running")
      if (runningTaskIndex !== -1) {
        setCurrentTaskIndex(runningTaskIndex)
      }
    }, 100)

    return () => clearInterval(interval)
  }, [tasks])

  const getTaskIcon = (type: string, status: string) => {
    const iconClass = "w-4 h-4"

    if (status === "running") {
      return <Loader2 className={`${iconClass} animate-spin text-blue-400`} />
    }

    if (status === "completed") {
      return <CheckCircle className={`${iconClass} text-green-400`} />
    }

    if (status === "error") {
      return <Circle className={`${iconClass} text-red-400`} />
    }

    switch (type) {
      case "file":
        return <FileText className={`${iconClass} text-gray-400`} />
      case "code":
        return <Code className={`${iconClass} text-gray-400`} />
      case "database":
        return <Database className={`${iconClass} text-gray-400`} />
      case "test":
        return <TestTube className={`${iconClass} text-gray-400`} />
      default:
        return <Circle className={`${iconClass} text-gray-400`} />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400"
      case "running":
        return "text-blue-400"
      case "error":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  return (
    <div className="bg-[#1a1a1a] border border-[#333] rounded-lg p-4 space-y-3">
      <div className="flex items-center gap-2 pb-2 border-b border-[#333]">
        <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
        <h3 className="text-sm font-medium text-white">{title}</h3>
        <span className="text-xs text-gray-400 ml-auto">
          {tasks.filter((t) => t.status === "completed").length}/{tasks.length}
        </span>
      </div>

      <div className="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
        {tasks.map((task, index) => (
          <div
            key={task.id}
            className={`flex items-center gap-3 p-2 rounded transition-colors ${
              task.status === "running" ? "bg-[#1e3a8a]/20" : ""
            }`}
          >
            {getTaskIcon(task.type, task.status)}
            <div className="flex-1 min-w-0">
              <p className={`text-sm truncate ${getStatusColor(task.status)}`}>{task.title}</p>
              {task.status === "running" && task.progress && (
                <div className="w-full bg-[#333] rounded-full h-1 mt-1">
                  <div
                    className="bg-blue-400 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${task.progress}%` }}
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
