"use client"

import React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  MessageSquare,
  Settings,
  Upload,
  Eye,
  Code,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Search,
  ArrowUp,
  User,
  Bot,
} from "lucide-react"
import AdminDashboard from "@/components/admin-dashboard"
import TaskList from "@/components/task-list"
import StreamingPreview from "@/components/streaming-preview"
import ConversationThreads from "@/components/conversation-threads"
import HoverTrigger from "@/components/hover-trigger"
import {
  AIConversation,
  AIConversationContent,
  AIConversationScrollButton,
} from "@/components/ui/kibo-ui/ai"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  tasks?: Task[]
}

interface Task {
  id: string
  title: string
  status: "pending" | "running" | "completed" | "error"
  type: "component" | "api" | "database" | "test"
  progress?: number
}

export default function AIDevInterface() {
  const [currentView, setCurrentView] = useState<"main" | "admin">("main")
  const [activeTab, setActiveTab] = useState<"preview" | "code">("preview")
  const [chatInput, setChatInput] = useState("")
  const [isStreaming, setIsStreaming] = useState(false)
  const [selectedModel, setSelectedModel] = useState("elite-fullstack")
  const [sidebarWidth, setSidebarWidth] = useState(400) // Default width for SSR
  const [isResizing, setIsResizing] = useState(false)

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  const resizeRef = useRef<HTMLDivElement>(null)

  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "ai",
      content:
        "Hello! I'm your AI coding assistant. I can help you build applications, write code, debug issues, and much more. What would you like to work on today?",
      timestamp: new Date("2024-01-01T10:00:00Z"),
    },
    {
      id: "2",
      type: "user",
      content: "I want to create a modern e-commerce platform with React and Node.js",
      timestamp: new Date("2024-01-01T10:02:00Z"),
    },
    {
      id: "3",
      type: "ai",
      content:
        "Perfect! I'll help you create a comprehensive e-commerce platform. Let me start by setting up the project structure and implementing the core components.",
      timestamp: new Date("2024-01-01T10:03:00Z"),
      tasks: [
        {
          id: "1",
          title: "Setting up React project structure",
          status: "completed",
          type: "component",
        },
        {
          id: "2",
          title: "Creating product catalog components",
          status: "running",
          type: "component",
          progress: 75,
        },
        {
          id: "3",
          title: "Implementing shopping cart functionality",
          status: "pending",
          type: "component",
        },
        {
          id: "4",
          title: "Setting up Node.js API endpoints",
          status: "pending",
          type: "api",
        },
      ],
    },
  ])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const handleSendMessage = () => {
    if (!chatInput.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: chatInput,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    setChatInput("")
    setIsStreaming(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "I'll help you with that! Let me start working on your request.",
        timestamp: new Date(),
        tasks: [
          {
            id: Date.now().toString(),
            title: "Processing your request",
            status: "running",
            type: "component",
            progress: 30,
          },
        ],
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsStreaming(false)
    }, 2000)
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return
      const newWidth = e.clientX - 16 // Account for padding
      setSidebarWidth(Math.max(300, Math.min(800, newWidth)))
    },
    [isResizing]
  )

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  if (currentView === "admin") {
    return <AdminDashboard onBack={() => setCurrentView("main")} />
  }

  return (
    <HoverTrigger onHoverChange={() => {}}>
      <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
        <ConversationThreads />

        {/* Custom Scrollbar Styles */}
        <style jsx global>{`
          .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
          }
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: none;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }
          .custom-scrollbar::-webkit-scrollbar-corner {
            background: transparent;
          }
        `}</style>

        {/* Top Navigation Bar */}
        <header className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xs">AI</span>
              </div>
              <div>
                <h1 className="text-white font-medium text-sm">AI Dev Ecosystem</h1>
                <p className="text-[#666] text-xs">Personal Workspace</p>
              </div>
            </div>
            <div className="flex items-center gap-1 text-xs text-[#666]">
              <span>Personal</span>
              <span>/</span>
              <span>The Orb</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              onClick={() => setCurrentView("admin")}
            >
              <Settings className="w-3 h-3 mr-2" />
              Settings
            </Button>
            <Button 
              size="sm" 
              className="h-8 px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
            >
              Publish
            </Button>
            <div className="w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2">
              <User className="w-4 h-4 text-[#888]" />
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden p-4 gap-4">
          {/* Left Sidebar - Chat */}
          <aside
            style={{ width: sidebarWidth }}
            className="bg-[#0a0a0a] border border-[#1a1a1a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-lg"
          >
            {/* Chat Header */}
            <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <h2 className="font-medium text-white text-sm">Chat</h2>
              </div>
              <div className="flex items-center gap-2">
                <button className="w-6 h-6 hover:bg-[#1a1a1a] rounded-md flex items-center justify-center transition-colors">
                  <MessageSquare className="w-4 h-4 text-[#666]" />
                </button>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <AIConversation className="h-full">
                <AIConversationContent className="flex-1 overflow-y-auto px-6 py-4 space-y-4 custom-scrollbar bg-[#0a0a0a]">
              {messages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.type === "user" ? "flex-row-reverse" : ""}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]"
                        }`}
                      >
                        {message.type === "user" ? "U" : "AI"}
                      </div>
                    </div>
                    <div className={`flex-1 max-w-[85%] ${message.type === "user" ? "text-right" : ""}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-[#111111] text-[#e5e5e5] border border-[#1a1a1a]"
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.type === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>

                  {/* Task List for AI messages */}
                  {message.type === "ai" && message.tasks && (
                    <div className="ml-10">
                      <TaskList tasks={message.tasks} title="Current Tasks" />
                    </div>
                  )}
                </div>
              ))}
                <AIConversationScrollButton />
                </AIConversationContent>
              </AIConversation>
            </div>




            {/* Chat Input */}
            <div className="shrink-0 p-6 border-t border-[#1a1a1a] bg-[#0a0a0a]">
              <div className="relative bg-[#111111] border border-[#1a1a1a] rounded-xl p-1 focus-within:border-blue-500 transition-colors shadow-lg">
                <textarea
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  placeholder="Message AI..."
                  className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none"
                  rows={1}
                  style={{ minHeight: '44px', maxHeight: '120px' }}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!chatInput.trim()}
                  className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
                >
                  <ArrowUp className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors"
              onMouseDown={handleMouseDown}
            />
          </aside>

          {/* Main Editor Area */}
          <main className="flex-1 flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden border border-[#1a1a1a] shadow-lg">
            {/* Editor Tabs */}
            <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
              <div className="flex items-center gap-1">
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeTab === "preview"
                      ? "bg-[#1a1a1a] text-white"
                      : "text-[#666] hover:text-white hover:bg-[#111111]"
                  }`}
                  onClick={() => setActiveTab("preview")}
                >
                  Preview
                </button>
                <button
                  className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeTab === "code"
                      ? "bg-[#1a1a1a] text-white"
                      : "text-[#666] hover:text-white hover:bg-[#111111]"
                  }`}
                  onClick={() => setActiveTab("code")}
                >
                  Code
                </button>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 bg-[#0a0a0a]">
              {activeTab === "preview" ? (
                <StreamingPreview />
              ) : (
                <div className="h-full flex">
                  {/* File Explorer */}
                  <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col">
                    <div className="px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between">
                      <h3 className="text-sm font-medium text-white">Explorer</h3>
                    </div>
                    <div className="flex-1 p-4">
                      <div className="text-xs text-[#666] space-y-2">
                        <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer transition-colors">📁 src</div>
                        <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors">📄 app.tsx</div>
                        <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors">📄 index.css</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Code Editor */}
                  <div className="flex-1 bg-[#0a0a0a] p-4">
                    <div className="h-full flex items-center justify-center bg-[#111111] rounded-lg border border-[#1a1a1a]">
                      <div className="text-center text-[#666] p-8">
                        <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p className="text-sm">Code editor will appear here</p>
                        <p className="text-xs mt-2 opacity-70">Select a file to start editing</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </HoverTrigger>
  )
}
