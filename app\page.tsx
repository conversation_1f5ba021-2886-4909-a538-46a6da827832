"use client"

import React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  MessageSquare,
  Settings,
  Upload,
  GitBranch,
  Eye,
  Code,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Search,
  Paperclip,
  ArrowUp,
  Minimize2,
  Maximize2,
  X,
  User,
  <PERSON><PERSON>,
} from "lucide-react"
import AdminDashboard from "@/components/admin-dashboard"
import TaskList from "@/components/task-list"
import StreamingPreview from "@/components/streaming-preview"
import ConversationThreads from "@/components/conversation-threads"
import HoverTrigger from "@/components/hover-trigger"
import {
  AIMessage,
  AIMessageContent,
  AIMessageAvatar,
  AIResponse,
  AIInput,
  AIInputTextarea,
  AIInputToolbar,
  AIInputTools,
  AIInputButton,
  AIInputSubmit,
  AIConversation,
  AIConversationContent,
  AIConversationScrollButton,
  AISuggestions,
  AISuggestion
} from "@/components/ui/kibo-ui/ai"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: Date
  tasks?: any[]
}

export default function AIDevInterface() {
  const [selectedModel, setSelectedModel] = useState("elite-fullstack")
  const [chatInput, setChatInput] = useState("")
  const [currentView, setCurrentView] = useState<"editor" | "admin">("editor")
  const [activeTab, setActiveTab] = useState<"preview" | "code">("code")
  const [sidebarWidth, setSidebarWidth] = useState(400) // Default width for SSR
  const [isResizing, setIsResizing] = useState(false)

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])
  const [isStreaming, setIsStreaming] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "ai",
      content:
        "Hello! I'm your AI coding assistant. I can help you build applications, write code, debug issues, and much more. What would you like to work on today?",
      timestamp: new Date("2024-01-01T10:00:00Z"),
    },
    {
      id: "2",
      type: "user",
      content: "I want to create a modern e-commerce platform with React and Node.js",
      timestamp: new Date("2024-01-01T10:02:00Z"),
    },
    {
      id: "3",
      type: "ai",
      content:
        "Perfect! I'll help you create a comprehensive e-commerce platform. Let me start by setting up the project structure and implementing the core components.",
      timestamp: new Date("2024-01-01T10:03:00Z"),
      tasks: [
        { id: "1", title: "Initialize Next.js project", status: "completed", type: "file" },
        { id: "2", title: "Set up TypeScript configuration", status: "completed", type: "code" },
        { id: "3", title: "Install required dependencies", status: "completed", type: "file" },
        { id: "4", title: "Create database schema", status: "running", type: "database", progress: 65 },
        { id: "5", title: "Implement user authentication", status: "pending", type: "code" },
        { id: "6", title: "Build product catalog", status: "pending", type: "code" },
        { id: "7", title: "Set up payment integration", status: "pending", type: "code" },
        { id: "8", title: "Write unit tests", status: "pending", type: "test" },
      ],
    },
  ])

  const [showThreads, setShowThreads] = useState(false)
  const [currentThreadId, setCurrentThreadId] = useState("1")

  const streamContent = [
    "🚀 Starting e-commerce platform setup...",
    "📦 Installing dependencies: react, next, typescript, tailwindcss",
    "⚙️  Configuring TypeScript and ESLint",
    "🗄️  Setting up database connection (PostgreSQL)",
    "🔐 Implementing authentication with NextAuth.js",
    "🛍️  Creating product models and schemas",
    "💳 Integrating Stripe payment processing",
    "🎨 Building responsive UI components",
    "🧪 Writing comprehensive test suites",
    "📱 Optimizing for mobile responsiveness",
    "🔍 Adding search and filtering functionality",
    "📊 Setting up analytics and monitoring",
    "🚀 Preparing for deployment...",
  ]

  const resizeRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return
      const newWidth = e.clientX - 16
      if (newWidth >= 250 && newWidth <= 600) {
        setSidebarWidth(newWidth)
      }
    },
    [isResizing],
  )

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
  }, [])

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = "col-resize"
      document.body.style.userSelect = "none"
    } else {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }
  }, [isResizing, handleMouseMove, handleMouseUp])

  React.useEffect(() => {
    const handleResize = () => {
      if (!isResizing) {
        // Maintain 1/3 proportion on window resize
        const newWidth = Math.floor((window.innerWidth - 48) / 3)
        if (newWidth >= 250 && newWidth <= window.innerWidth * 0.6) {
          setSidebarWidth(newWidth)
        }
      }
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [isResizing])

  const handleSendMessage = () => {
    if (!chatInput.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: chatInput,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    setChatInput("")

    // Simulate AI response with tasks
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "I'll help you implement that feature. Let me break this down into actionable tasks.",
        timestamp: new Date(),
        tasks: [
          { id: "t1", title: "Analyze requirements", status: "completed", type: "code" },
          { id: "t2", title: "Design component structure", status: "running", type: "code", progress: 40 },
          { id: "t3", title: "Implement core functionality", status: "pending", type: "code" },
          { id: "t4", title: "Add error handling", status: "pending", type: "code" },
          { id: "t5", title: "Write tests", status: "pending", type: "test" },
        ],
      }
      setMessages((prev) => [...prev, aiResponse])
    }, 1000)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const handleSelectThread = (threadId: string) => {
    setCurrentThreadId(threadId)
    // Here you would load the conversation data for the selected thread
    console.log("Selected thread:", threadId)
    setShowThreads(false) // Hide threads after selection
  }

  if (currentView === "admin") {
    return <AdminDashboard onBack={() => setCurrentView("editor")} />
  }

  return (
    <HoverTrigger onHoverChange={setShowThreads}>
      <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
        <ConversationThreads
          isVisible={showThreads}
          onSelectThread={handleSelectThread}
          currentThreadId={currentThreadId}
        />
        {showThreads && (
          <div
            className="fixed inset-0 bg-black/20 z-40 transition-opacity duration-300"
            onClick={() => setShowThreads(false)}
          />
        )}
        {/* Custom Scrollbar Styles */}
        <style jsx global>{`
          .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
          }
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: none;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }
          .custom-scrollbar::-webkit-scrollbar-corner {
            background: transparent;
          }
        `}</style>

        {/* Top Menu Bar */}
        <header className="h-7 bg-[#0F0F10] border-b border-[#2a2a2a] flex items-center justify-between px-3 text-xs">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="font-bold text-white">APB</span>
              <span className="font-bold text-blue-500">X</span>
              <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
            </div>
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Personal
              </span>
              <span className="text-[#888]">Free</span>
              <span className="text-[#888]">/</span>
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                The Orb
              </span>
              <span className="text-[#888]">/</span>
              <span>The Orb</span>
              <div className="flex items-center gap-1 ml-2">
                <div className="w-3 h-3 bg-[#333] rounded-sm flex items-center justify-center">
                  <div className="w-1 h-1 bg-[#666] rounded-full"></div>
                </div>
                <span className="text-[#888] text-xs">Private</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="h-6 px-2 bg-[#333] hover:bg-[#444] text-xs border-0"
              onClick={() => setCurrentView("admin")}
            >
              <Settings className="w-3 h-3 mr-1" />
              Admin
            </Button>
            <Button size="sm" className="h-6 px-2 bg-[#333] hover:bg-[#444] text-xs border-0">
              <Settings className="w-3 h-3 mr-1" />
            </Button>
            <Button size="sm" className="h-6 px-2 bg-[#333] hover:bg-[#444] text-xs border-0">
              <Upload className="w-3 h-3 mr-1" />
            </Button>
            <Button size="sm" className="h-6 px-2 bg-[#333] hover:bg-[#444] text-xs border-0">
              <GitBranch className="w-3 h-3 mr-1" />
            </Button>
            <Button size="sm" className="h-6 px-3 bg-white text-black hover:bg-gray-200 text-xs font-medium">
              Publish
            </Button>
            <div className="flex items-center gap-1 ml-2">
              <button className="w-6 h-6 hover:bg-[#333] rounded flex items-center justify-center">
                <Minimize2 className="w-3 h-3" />
              </button>
              <button className="w-6 h-6 hover:bg-[#333] rounded flex items-center justify-center">
                <Maximize2 className="w-3 h-3" />
              </button>
              <button className="w-6 h-6 hover:bg-red-600 rounded flex items-center justify-center">
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden p-4 gap-4">
          {/* Left Sidebar - Chat */}
          <aside
            style={{ width: sidebarWidth }}
            className="bg-[#0F0F10] border border-[#2a2a2a] flex flex-col rounded-xl overflow-hidden relative h-full shadow-2xl"
          >
            <div className="px-6 py-3 h-12 border-b border-[#2a2a2a] flex items-center">
              <div className="flex items-center gap-3">
                <MessageSquare className="w-5 h-5 text-blue-400" />
                <h2 className="font-medium text-white text-base">Chat</h2>
                <span className="text-xs text-[#888] ml-auto font-medium">AI Assistant</span>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <AIConversation className="h-full">
                <AIConversationContent className="flex-1 overflow-y-auto p-6 space-y-6 custom-scrollbar bg-[#0F0F10]">
              {messages.map((message) => (
                <div key={message.id} className="space-y-3">
                  <AIMessage from={message.type === "user" ? "user" : "assistant"}>
                    <AIMessageAvatar
                      src=""
                      name={message.type === "user" ? "You" : "AI"}
                      className={
                        message.type === "user"
                          ? "bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg"
                          : "bg-gradient-to-br from-gray-700 to-gray-800 shadow-lg"
                      }
                    />
                    <AIMessageContent
                      className={
                        message.type === "user"
                          ? "bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-2xl border-0 shadow-lg"
                          : "bg-[#1a1a1a] text-[#e5e5e5] rounded-2xl border border-[#2a2a2a] shadow-sm"
                      }
                    >
                      <AIResponse className="text-inherit [&>*]:text-inherit [&>*:first-child]:mt-0 [&>*:last-child]:mb-0">
                        {message.content}
                      </AIResponse>
                      <div className={`text-xs opacity-70 mt-2 ${message.type === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </AIMessageContent>
                  </AIMessage>

                  {/* Task List for AI messages */}
                  {message.type === "ai" && message.tasks && (
                    <div className="ml-10">
                      <TaskList tasks={message.tasks} title="Current Tasks" />
                    </div>
                  )}
                </div>
              ))}
                <AIConversationScrollButton />
                </AIConversationContent>
              </AIConversation>
            </div>





            {/* Chat Input */}
            <div className="shrink-0 p-6 border-t border-[#2a2a2a] bg-[#0F0F10]">
              <AIInput className="bg-[#1a1a1a] border-[#2a2a2a] focus-within:border-blue-400 transition-all duration-300 rounded-xl shadow-lg hover:shadow-xl">
                <AIInputTextarea
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="What would you like to build today?"
                  className="bg-transparent text-sm text-white placeholder:text-[#666] font-medium"
                  minHeight={48}
                  maxHeight={140}
                />
                <AIInputToolbar>
                  <AIInputTools>
                    <AIInputButton>
                      <Paperclip className="w-4 h-4" />
                    </AIInputButton>
                  </AIInputTools>
                  <AIInputSubmit
                    onClick={handleSendMessage}
                    disabled={!chatInput.trim()}
                    status={isStreaming ? "streaming" : "ready"}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:bg-[#2a2a2a] disabled:text-[#666] shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg"
                  />
                </AIInputToolbar>
              </AIInput>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors"
              onMouseDown={handleMouseDown}
            />
          </aside>

          {/* Main Editor Area */}
          <main className="flex-1 flex flex-col bg-[#0F0F10] rounded-xl overflow-hidden border border-[#2a2a2a] shadow-2xl">
            {/* Editor Tabs */}
            <div className="h-12 bg-[#1a1a1a] border-b border-[#2a2a2a] flex items-center px-6">
              <div className="flex items-center gap-4">
                <div
                  className={`flex items-center gap-2 px-4 py-2 cursor-pointer transition-all duration-300 rounded-lg ${
                    activeTab === "preview"
                      ? "bg-[#2a2a2a] border border-blue-400 text-white shadow-lg"
                      : "text-[#888] hover:text-white hover:bg-[#222]"
                  }`}
                  onClick={() => setActiveTab("preview")}
                >
                  <Eye className="w-3 h-3" />
                  <span className="text-xs">Preview</span>
                </div>
                <div
                  className={`flex items-center gap-2 px-4 py-2 cursor-pointer transition-all duration-300 rounded-lg ${
                    activeTab === "code"
                      ? "bg-[#2a2a2a] border border-blue-400 text-white shadow-lg"
                      : "text-[#888] hover:text-white hover:bg-[#222]"
                  }`}
                  onClick={() => setActiveTab("code")}
                >
                  <Code className="w-3 h-3" />
                  <span className="text-xs">Code</span>
                </div>
              </div>

              <div className="ml-auto flex items-center gap-2">
                <button className="p-1 hover:bg-[#333] rounded">
                  <ChevronLeft className="w-4 h-4 text-[#888]" />
                </button>
                <button className="p-1 hover:bg-[#333] rounded">
                  <ChevronRight className="w-4 h-4 text-[#888]" />
                </button>
                <button className="p-1 hover:bg-[#333] rounded">
                  <RotateCcw className="w-4 h-4 text-[#888]" />
                </button>
                <div className="w-px h-4 bg-[#333] mx-2"></div>
                <div className="bg-[#333] rounded px-2 py-1 flex items-center gap-2 min-w-32">
                  <Search className="w-3 h-3 text-[#888]" />
                  <span className="text-xs text-[#888]">/</span>
                </div>
                <div className="ml-4 flex items-center gap-2">
                  <button className="p-1 hover:bg-[#333] rounded">
                    <Upload className="w-4 h-4 text-[#888]" />
                  </button>
                  <span className="text-xs text-[#888]">v9</span>
                  <button className="p-1 hover:bg-[#333] rounded">
                    <ChevronLeft className="w-3 h-3 text-[#888] rotate-90" />
                  </button>
                  <div className="flex items-center gap-1">
                    <button className="p-1 hover:bg-[#333] rounded">
                      <div className="w-3 h-3 border border-[#888] rounded-sm"></div>
                    </button>
                    <button className="p-1 hover:bg-[#333] rounded">
                      <div className="w-3 h-3 border border-[#888] rounded-sm flex items-center justify-center">
                        <div className="w-1 h-1 bg-[#888]"></div>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === "preview" ? (
              <StreamingPreview
                isStreaming={isStreaming}
                streamContent={streamContent}
                onToggleStream={() => setIsStreaming(!isStreaming)}
              />
            ) : (
              /* Code Editor Content */
              <div className="flex-1 flex bg-[#0F0F10]">
                {/* File Explorer */}
                <div className="w-64 bg-[#1a1a1a] border-r border-[#2a2a2a] flex flex-col">
                  <div className="px-4 py-3 border-b border-[#2a2a2a] flex items-center justify-between">
                    <span className="text-xs font-medium text-[#888] uppercase tracking-wide">Explorer</span>
                  </div>
                  <div className="flex-1 p-2">
                    <div className="space-y-1">
                      <div className="flex items-center gap-1 text-xs text-white">
                        <ChevronRight className="w-3 h-3 rotate-90" />
                        <span>app</span>
                      </div>
                      <div className="ml-4 space-y-1">
                        <div className="flex items-center gap-2 text-xs text-[#888] hover:text-white cursor-pointer py-1">
                          <div className="w-3 h-3 bg-blue-500 rounded-sm flex items-center justify-center text-[10px] font-bold text-white">
                            TS
                          </div>
                          <span>loading.tsx</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-white cursor-pointer py-1 bg-[#333] rounded px-1">
                          <div className="w-3 h-3 bg-blue-500 rounded-sm flex items-center justify-center text-[10px] font-bold text-white">
                            TS
                          </div>
                          <span>page.tsx</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Code Editor */}
                <div className="flex-1 flex flex-col">
                  {/* File breadcrumb */}
                  <div className="h-8 bg-[#1e1e1e] border-b border-[#333333] flex items-center px-4 text-xs text-[#888]">
                    <span>app</span>
                    <ChevronRight className="w-3 h-3 mx-1" />
                    <span className="text-white">page.tsx</span>
                  </div>

                  {/* Code content */}
                  <div className="flex-1 flex">
                    {/* Line numbers */}
                    <div className="w-12 bg-[#1e1e1e] border-r border-[#333333] flex flex-col text-xs text-[#888] font-mono">
                      {Array.from({ length: 50 }, (_, i) => (
                        <div key={i + 1} className="h-5 flex items-center justify-end pr-2">
                          {i + 1}
                        </div>
                      ))}
                    </div>

                    {/* Code editor area */}
                    <div className="flex-1 bg-[#1e1e1e] p-4 font-mono text-sm overflow-auto custom-scrollbar">
                      <div className="space-y-1">
                        <div>
                          <span className="text-blue-400">"use client"</span>
                        </div>
                        <div></div>
                        <div>
                          <span className="text-blue-400">import</span> <span className="text-yellow-400">{"{"}</span>{" "}
                          <span className="text-cyan-400">useState</span> <span className="text-yellow-400">{"}"}</span>{" "}
                          <span className="text-blue-400">from</span> <span className="text-green-400">"react"</span>
                        </div>
                        <div>
                          <span className="text-blue-400">import</span> <span className="text-yellow-400">{"{"}</span>{" "}
                          <span className="text-cyan-400">Button</span> <span className="text-yellow-400">{"}"}</span>{" "}
                          <span className="text-blue-400">from</span>{" "}
                          <span className="text-green-400">"@/components/ui/button"</span>
                        </div>
                        <div>
                          <span className="text-blue-400">import</span> <span className="text-yellow-400">{"{"}</span>{" "}
                          <span className="text-cyan-400">Input</span> <span className="text-yellow-400">{"}"}</span>{" "}
                          <span className="text-blue-400">from</span>{" "}
                          <span className="text-green-400">"@/components/ui/input"</span>
                        </div>
                        <div>
                          <span className="text-blue-400">import</span> <span className="text-yellow-400">{"{"}</span>{" "}
                          <span className="text-cyan-400">
                            Select, SelectContent, SelectItem, SelectTrigger, SelectValue
                          </span>{" "}
                          <span className="text-yellow-400">{"}"}</span> <span className="text-blue-400">from</span>{" "}
                          <span className="text-green-400">"@/components/ui/select"</span>
                        </div>
                        <div>
                          <span className="text-blue-400">import</span> <span className="text-yellow-400">{"{"}</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">MessageSquare,</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">Settings,</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">Upload,</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">GitBranch,</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">Eye,</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-cyan-400">Code,</span>
                        </div>
                        <div>
                          <span className="text-yellow-400">{"}"}</span> <span className="text-blue-400">from</span>{" "}
                          <span className="text-green-400">"lucide-react"</span>
                        </div>
                        <div></div>
                        <div>
                          <span className="text-blue-400">export default function</span>{" "}
                          <span className="text-yellow-400">AIDevInterface</span>
                          <span className="text-white">()</span> <span className="text-yellow-400">{"{"}</span>
                        </div>
                        <div className="ml-4">
                          <span className="text-blue-400">const</span>{" "}
                          <span className="text-white">[selectedModel, setSelectedModel]</span>{" "}
                          <span className="text-blue-400">=</span> <span className="text-yellow-400">useState</span>
                          <span className="text-white">(</span>
                          <span className="text-green-400">"elite-fullstack"</span>
                          <span className="text-white">)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </main>
        </div>
      </div>
    </HoverTrigger>
  )
}
