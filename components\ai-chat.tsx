"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bo<PERSON>, User, Send, Paperclip, Mic, Square, Copy, ThumbsUp, Thum<PERSON>Down, Sparkles } from "lucide-react"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  isStreaming?: boolean
  metadata?: {
    model?: string
    tokens?: number
    confidence?: number
  }
}

interface AIChatProps {
  messages: Message[]
  onSendMessage: (message: string) => void
  isLoading?: boolean
  model?: string
}

export default function AIChat({ messages, onSendMessage, isLoading = false, model = "GPT-4" }: AIChatProps) {
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSend = () => {
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim())
      setInput("")
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  return (
    <div className="flex flex-col h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar">
        {messages.map((message) => (
          <div key={message.id} className={`flex gap-3 ${message.role === "user" ? "flex-row-reverse" : ""}`}>
            <div className="flex-shrink-0">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.role === "user" ? "bg-blue-500" : "bg-gray-600"
                }`}
              >
                {message.role === "user" ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>
            </div>

            <div className={`flex-1 max-w-[80%] ${message.role === "user" ? "text-right" : ""}`}>
              <Card
                className={`${
                  message.role === "user"
                    ? "bg-blue-500 text-white border-blue-500"
                    : "bg-[#1a1a1a] border-[#333] text-gray-100"
                }`}
              >
                <CardContent className="p-3">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>

                  {message.isStreaming && (
                    <div className="flex items-center gap-2 mt-2 text-xs opacity-70">
                      <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                      <span>AI is thinking...</span>
                    </div>
                  )}

                  {message.metadata && message.role === "assistant" && (
                    <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-600">
                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {message.metadata.model}
                        </Badge>
                        {message.metadata.tokens && <span>{message.metadata.tokens} tokens</span>}
                        {message.metadata.confidence && (
                          <span className="flex items-center gap-1">
                            <Sparkles className="w-3 h-3" />
                            {Math.round(message.metadata.confidence * 100)}%
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                          onClick={() => copyMessage(message.content)}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <ThumbsUp className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <ThumbsDown className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className={`text-xs text-gray-500 mt-1 ${message.role === "user" ? "text-right" : ""}`}>
                {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <Card className="bg-[#1a1a1a] border-[#333]">
              <CardContent className="p-3">
                <div className="flex items-center gap-2 text-gray-400">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                    <div
                      className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    />
                    <div
                      className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    />
                  </div>
                  <span className="text-sm">AI is processing...</span>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t border-[#333] p-4">
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="outline" className="text-xs">
            {model}
          </Badge>
          <div className="w-2 h-2 bg-green-400 rounded-full" />
          <span className="text-xs text-gray-400">Online</span>
        </div>

        <div className="relative">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask AI anything..."
            className="bg-[#1e1e1e] border-[#333] pr-20 focus:border-blue-500 transition-colors"
            disabled={isLoading}
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={() => setIsRecording(!isRecording)}>
              {isRecording ? <Square className="w-4 h-4 text-red-400" /> : <Mic className="w-4 h-4 text-gray-400" />}
            </Button>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <Paperclip className="w-4 h-4 text-gray-400" />
            </Button>
            <Button
              size="sm"
              className="h-8 w-8 p-0 bg-blue-500 hover:bg-blue-600"
              onClick={handleSend}
              disabled={!input.trim() || isLoading}
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
