"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  MessageSquare,
  Search,
  Plus,
  Clock,
  Star,
  Archive,
  Trash2,
  Code,
  Database,
  Globe,
  Smartphone,
  ChevronRight,
  Bot,
  User,
} from "lucide-react"

interface ChatThread {
  id: string
  title: string
  lastMessage: string
  timestamp: Date
  messageCount: number
  isStarred: boolean
  isArchived: boolean
  projectType: "web" | "mobile" | "api" | "database" | "general"
  participants: ("user" | "ai")[]
  status: "active" | "completed" | "paused"
}

interface ConversationThreadsProps {
  isVisible: boolean
  onSelectThread: (threadId: string) => void
  currentThreadId?: string
}

export default function ConversationThreads({ isVisible, onSelectThread, currentThreadId }: ConversationThreadsProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilter, setActiveFilter] = useState<"all" | "starred" | "archived">("all")

  const [threads] = useState<ChatThread[]>([
    {
      id: "1",
      title: "E-commerce Platform",
      lastMessage: "Perfect! I'll help you create a comprehensive e-commerce platform...",
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      messageCount: 15,
      isStarred: true,
      isArchived: false,
      projectType: "web",
      participants: ["user", "ai"],
      status: "active",
    },
    {
      id: "2",
      title: "React Native Mobile App",
      lastMessage: "Let's set up the navigation structure for your mobile app...",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      messageCount: 8,
      isStarred: false,
      isArchived: false,
      projectType: "mobile",
      participants: ["user", "ai"],
      status: "active",
    },
    {
      id: "3",
      title: "REST API Development",
      lastMessage: "I've implemented the authentication endpoints with JWT...",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      messageCount: 23,
      isStarred: true,
      isArchived: false,
      projectType: "api",
      participants: ["user", "ai"],
      status: "completed",
    },
    {
      id: "4",
      title: "Database Schema Design",
      lastMessage: "The normalized schema looks good. Here are the relationships...",
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      messageCount: 12,
      isStarred: false,
      isArchived: false,
      projectType: "database",
      participants: ["user", "ai"],
      status: "completed",
    },
    {
      id: "5",
      title: "Portfolio Website",
      lastMessage: "Your portfolio looks amazing! The animations are smooth...",
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      messageCount: 19,
      isStarred: false,
      isArchived: true,
      projectType: "web",
      participants: ["user", "ai"],
      status: "completed",
    },
    {
      id: "6",
      title: "Machine Learning Pipeline",
      lastMessage: "The model training is complete. Accuracy: 94.2%...",
      timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      messageCount: 31,
      isStarred: true,
      isArchived: false,
      projectType: "general",
      participants: ["user", "ai"],
      status: "paused",
    },
  ])

  const getProjectIcon = (type: string) => {
    switch (type) {
      case "web":
        return <Globe className="w-4 h-4" />
      case "mobile":
        return <Smartphone className="w-4 h-4" />
      case "api":
        return <Code className="w-4 h-4" />
      case "database":
        return <Database className="w-4 h-4" />
      default:
        return <MessageSquare className="w-4 h-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500"
      case "completed":
        return "bg-blue-500"
      case "paused":
        return "bg-yellow-500"
      default:
        return "bg-gray-500"
    }
  }

  const formatTimestamp = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const filteredThreads = threads
    .filter((thread) => {
      if (activeFilter === "starred" && !thread.isStarred) return false
      if (activeFilter === "archived" && !thread.isArchived) return false
      if (activeFilter === "all" && thread.isArchived) return false
      return (
        thread.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        thread.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

  return (
    <div
      className={`fixed left-0 top-0 h-full w-80 bg-[#1a1a1a] border-r border-[#333] z-50 transform transition-transform duration-300 ease-out ${
        isVisible ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      {/* Header */}
      <div className="h-14 bg-[#252526] border-b border-[#333] flex items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-blue-400" />
          <h2 className="font-semibold text-white">Conversations</h2>
        </div>
        <Button size="sm" className="h-7 w-7 p-0 bg-blue-500 hover:bg-blue-600">
          <Plus className="w-4 h-4" />
        </Button>
      </div>

      {/* Search */}
      <div className="p-4 border-b border-[#333]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search conversations..."
            className="pl-10 bg-[#2a2a2a] border-[#444] text-sm h-9 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Filters */}
      <div className="px-4 py-2 border-b border-[#333]">
        <div className="flex gap-2">
          {[
            { key: "all", label: "All", icon: MessageSquare },
            { key: "starred", label: "Starred", icon: Star },
            { key: "archived", label: "Archived", icon: Archive },
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveFilter(key as any)}
              className={`flex items-center gap-1 px-3 py-1 rounded text-xs transition-colors ${
                activeFilter === key ? "bg-blue-500 text-white" : "bg-[#333] text-gray-300 hover:bg-[#444]"
              }`}
            >
              <Icon className="w-3 h-3" />
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Thread List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {filteredThreads.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500">
            <MessageSquare className="w-8 h-8 mb-2 opacity-50" />
            <p className="text-sm">No conversations found</p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {filteredThreads.map((thread) => (
              <div
                key={thread.id}
                onClick={() => onSelectThread(thread.id)}
                className={`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                  currentThreadId === thread.id
                    ? "bg-blue-500/20 border border-blue-500/30"
                    : "hover:bg-[#2a2a2a] border border-transparent"
                }`}
              >
                {/* Thread Header */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="text-blue-400 flex-shrink-0">{getProjectIcon(thread.projectType)}</div>
                    <h3 className="font-medium text-white text-sm truncate">{thread.title}</h3>
                    <div className={`w-2 h-2 rounded-full flex-shrink-0 ${getStatusColor(thread.status)}`} />
                  </div>
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {thread.isStarred && <Star className="w-3 h-3 text-yellow-400 fill-current" />}
                    <button className="p-1 hover:bg-[#444] rounded">
                      <Trash2 className="w-3 h-3 text-gray-400" />
                    </button>
                  </div>
                </div>

                {/* Last Message */}
                <p className="text-xs text-gray-400 mb-2 line-clamp-2 leading-relaxed">{thread.lastMessage}</p>

                {/* Thread Footer */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      {thread.participants.map((participant, index) => (
                        <div
                          key={index}
                          className={`w-4 h-4 rounded-full flex items-center justify-center ${
                            participant === "user" ? "bg-blue-500" : "bg-gray-600"
                          }`}
                        >
                          {participant === "user" ? (
                            <User className="w-2 h-2 text-white" />
                          ) : (
                            <Bot className="w-2 h-2 text-white" />
                          )}
                        </div>
                      ))}
                    </div>
                    <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                      {thread.messageCount}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">{formatTimestamp(thread.timestamp)}</span>
                    <ChevronRight className="w-3 h-3 text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="h-12 bg-[#252526] border-t border-[#333] flex items-center justify-between px-4">
        <div className="flex items-center gap-2 text-xs text-gray-400">
          <Clock className="w-3 h-3" />
          <span>{filteredThreads.length} conversations</span>
        </div>
        <Button size="sm" variant="ghost" className="h-6 px-2 text-xs">
          <Archive className="w-3 h-3 mr-1" />
          Archive All
        </Button>
      </div>
    </div>
  )
}
